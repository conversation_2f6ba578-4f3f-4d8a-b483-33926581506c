# OpenRA 游戏 AI 副官自然语言控制技术Hackathon策划案

## 比标和背景介绍

本次比赛旨在探索 **大型语言模型（LLM）与即时战略游戏的融合应用**，促进自然语言处理技术在游戏领域的创新。通过让参赛者构建 AI 副官（虚拟助手），能够根据人类的语音或文字指令实时控制经典 RTS 游戏《红色警戒》的开源引擎 OpenRA，模拟出人类"副官"听令作战的场景。

**背景：** 近年来，ChatGPT、Claude 等大型语言模型展现了强大的理解和执行复杂指令的能力。而在游戏领域，也已经有一定研究，LLM 可以将玩家的高层策略翻译为可执行的详细计划，实现对游戏单位的自然语言指挥。OpenRA 是一个开源、跨平台的实时战略游戏引擎，重现了《命令与征服：红色警戒》等经典游戏[OpenRA(github)](https://github.com/OpenRA/OpenRA)。借助其可定制性，我们对 OpenRA 进行了修改，使其能够接受来自 LLM 的外部指令，如建造建筑、生产单位、移动军队、攻击敌军以及查询战场信息等操作。比赛希望借此 **推动人机交互的新形式**：玩家只需发出日常语言指令，AI 副官即可智能解析并在游戏中执行，为 **人机协同决策** 和 **游戏 AI 创新** 探索新的可能。

希望大家通过竞赛交流，沉淀一套开源的 **"LLM+游戏"解决方案**，为后续研究和应用提供参考。

## 参赛资格和方式

**参赛资格：** （Todo：待补充）

**不限制参赛者使用任何编程语言或工具**，只要最终作品符合要求即可。

**参赛方式：** （Todo：待补充）

## 比赛阶段与时间安排


确保所有API可用：6月22  
测试选手可以初步测试：6月29  
准备完成所有资料（待封装）：7月6   
确保自动化测试流程无误：7月8  
准备好全部发布环境：7月10号  
比赛开始：7月13

### 阶段一：线上预选（提交制）

- 所有参赛者需在截止日前提交以下材料至指定平台或邮箱：
  - 项目代码（含控制逻辑、接口交互模块等）
  - 所有参赛作品必须使用 Docker 进行封装提交
  - **演示视频**（5-10分钟，含自然语言交互及游戏效果展示）
  - 简要说明文档（说明设计亮点、使用模型）

- 截止时间：**2025年9月1日 23:59**

### AI副官自然语言控制评测分级体系（由浅入深）

为确保对不同层次AI系统的能力进行合理评价，预选采用6级能力测试结构，每一级代表一种更高复杂度的任务能力。前5项测试均以**中文自然语言指令**为输入，最后一项为**语音输入**，AI副官需通过 LLM 模块理解并驱动游戏完成响应操作。

---

## ✅ 评分方式说明

- 每一级测试设若干场景，**每个场景独立评分**。
- 每个场景满分 5 分，具体标准：
  - 5 分：完整理解 + 正确执行 + 反馈清晰
  - 3 分：部分理解 + 关键目标完成
  - 1 分：误解或偏差明显
  - 0 分：无反应或完全错误执行
- 每级得分为该级所有场景平均值
- 每队所有测试总分为各级得分加权求和

---

## 📘 等级测试结构

---

### 1️⃣ 基础动作执行能力（Basic Commands）

**目标：** 检验副官是否能准确执行基础的、单一的自然语言任务。

**测试指令示例：**

- “建造一座电厂”
- “训练三个步兵”
- “派那个坦克去基地右侧”
- “优先攻击敌方电厂”

**考察点：**

- 动作匹配正确（语义识别准确）
- 参数理解正确（数量、目标位置）
- 是否明确反馈执行状态

---

### 2️⃣ 连续有序指令链执行能力（Logical Sequence Planning）

**目标：** 测试副官能否根据较长的、多步指令计划进行顺序执行。

**测试指令示例：**

- “展开基地车，然后建电厂、兵营，再造3个步兵”
- “造矿场后，再修建一个战车工厂”
- “修好建筑以后再造步兵，防守基地左侧”

**考察点：**

- 步骤顺序是否正确
- 每步动作是否执行完成后进入下一步
- 是否有明确阶段性反馈（如“电厂建好了”）（可选）

---

### 3️⃣ 不完全/条件性指令理解能力（Conditional Action Resolution）

**目标：** 测试系统在“缺少前置条件”时是否能识别并尝试补足/应对。

**测试指令示例：**

- “造3个步兵”（但当前无兵营）
- “派坦克进攻”（没有坦克）
- “修复基地”（基地未受损）

**考察点：**

- 是否能识别无法执行的原因
- 是否尝试自动修复（如先建兵营）（或者提出自己的见解）
- 是否给出“我现在无法造步兵，需要建兵营”的反馈

---

### 4️⃣ 抽象策略性命令理解（Abstract Tactical Goals）

**目标：** 检验 AI 副官对高层战略语句的分解与执行能力。

**测试指令示例：**

- “全军出击”
- “两路包夹敌方基地”
- “防守我方左侧矿场”
- “尽快消灭敌人采矿单位”

**考察点：**

- 是否主动分组/调动单位
- 是否有地理/目标优先级意识
- 行动是否体现指令意图

---

### 5️⃣ 模糊语言处理能力（Ambiguous/Implicit Commands）

**目标：** 测试副官是否能处理模糊表达、上下文引用、非标准语言。

**测试指令示例：**

- “帮我造那个以前建过的东西”
- “把那个快没血的送去修一下”
- “往那个敌人多的地方打”

**考察点：**

- 请求澄清（“你指的是兵营吗？”）或 利用上下文完成合理推测
- 是否提供推测结果反馈

---

### 6️⃣ 语音输入能力（Speech Understanding）

**目标：** 验证系统是否能将**真实语音输入**准确识别、理解并完成前述全部类型任务。

**测试形式：**

- 测试者通过麦克风读出上述各级指令（可随机抽选4条）
- 系统接收语音→转文字→解析→执行

**考察点：**

- 语音识别是否准确（容错率 ≤ 10%）
- 后续动作是否一致（与文字版测试结果匹配）
- 执行延迟是否可接受（≤ 2秒内反应）

---

## 🏁 总分计算建议（示例）

| 测试等级       | 场景数量 | 建议占比 |
|----------------|----------|-----------|
| 基础动作       | 4~6        |  15%       |
| 连续任务       | 3~4        |  20%       |
| 条件判断       | 2~3        |  15%       |
| 抽象命令       | 3~4        |  20%       |
| 模糊指令       | 2~3        |  15%       |
| 语音指令       | 7~10       | 15%       |
| **总计**        | —        | 100%      |

---

## 🧩 附注

- 若部分功能模块未实现（如无语音），该项可不评分，但影响综合评定。
- 所有评分由 2~3 名评委交叉给分，去极值取平均，确保客观性。

- 结果：选出 **前8支队伍** 进入线下决赛

---
### 阶段二：线下决赛（现场对抗）

- 时间与地点：9月13日现场
- 每支队伍配备统一配置计算机（联网 + 官方提供AI API）
- 决赛共包含 3 类比赛模式，每类比赛**独立淘汰制**，各自决出前四名获得积分，按三场比赛积分和评选总名次

---

## 🎮 比赛模式设置（共三项，独立排名）

---

### 🧭 比赛一：人机协同作战（完整流程） - **BO3 淘汰赛**

- **起始状态**：从标准开局开始（基地车未部署）
- **输入方式**：人类选手通过**语音或文字**自然语言控制 AI 副官
- **目标**：人类+AI 合作完成全流程发展与作战

- **赛制：** BO3 淘汰赛（单败），胜者晋级，最终决出该模式冠军队伍

### 🔥 比赛二：人机协同作战（战斗控制重点） - **淘汰赛**

- **起始状态**：进入一场**中后期激烈战斗**，已拥有单位与建筑
- **输入方式**：人类通过语音或文字实时下达战斗指令
- **目标**：完成战场调度、反击、压制等操作，进行攻防转化战术演示

- **每场比赛包含两轮（攻防转换），双方互换控制阵营**
- 每轮获得独立评分，累加积分决定胜负
- 赛制为淘汰晋级制，最终选出该模式第一名

- （Todo）这里可能需要评委参与，还需要细想  

**评分维度（每轮最多 10 分）：**

| 项 | 说明 |
|----|------|
| 快速调度能力 | 是否能快速响应局势变化 |
| 指令拆解合理性 | 是否能有效下达多单位协作命令 |
| 战局推进 | 攻方是否压制有效，守方是否成功防御 |
| 错误容错 | 模糊指令是否产生歧义、是否造成失误 |

---

### 🤖 比赛三：AI 独立作战（全自动） - **BO3 淘汰赛**

- **起始状态**：标准开局，无任何人类操作
- **输入方式**：全自动，由 AI 自行完成发展、生产、作战
- **目标**：尽可能打出高质量完整战局，打败预设 AI 对手

- **赛制：** BO3 单败淘汰赛，评委按三局平均表现评分，决出最终胜者

---

## 🧮 总体说明

- 三项比赛独立评分与淘汰流程，各决出一支冠军队伍和前四名
- 每项比赛的胜出队伍将获得相应项目奖项与荣誉
- 按三场比赛积分和评选总名次

---

## ⚠️ 注意事项

- 禁止使用硬编码、定序脚本或敌方坐标表
- 所有操作必须实时执行，不允许预设宏命令或事先录制
- 使用语音控制者必须接入实际语音识别组件

---

## 修改版 OpenRA 功能详解

为满足本次比赛需求，我们对 OpenRA 游戏进行了定制修改。以下详细说明 **修改版 OpenRA 支持的功能** 及其实现方式：

- **简化兵种和阵营：**

- **比赛模式限定：** 游戏限定在 **1v1 对战** 模式，即玩家（由参赛者的AI副官控制）对抗一个敌方 AI。双方阵营均限定为 **苏联阵营**，地图固定使用提供的1v1标准小地图，地图大小适中，含基本资源点。我们已禁用所有超级武器（如核弹袭击、铁幕装置等），以简化指挥内容和避免比赛中出现毁灭性武器。游戏开始时，双方各自拥有标准开局基地和若干初始单位，资源和科技树规则与原版《红色警戒》苏联方一致。
- **外部指令接口：** 修改版 OpenRA 内建一个 **指令接收模块**，实时监听来自外部的控制指令。该模块在游戏启动时自动运行，通过本地网络端口（默认端口例如5000，可在配置文件中修改）等待指令输入。指令采用 **JSON 格式的消息**，可以通过HTTP POST、WebSocket等方式发送到游戏。每条指令会被解析并转化为相应的游戏动作。比如，收到建造指令时，游戏将在可行位置开始建造相应建筑；收到移动指令时，将选定单位执行移动；查询指令则返回所需的状态信息。整个过程类似于游戏内置AI执行逻辑，但决策来自外部指令流。
- **支持的操作类别：** 目前接口支持以下几类操作，能够基本覆盖游戏常用指挥：
  - **建造/生产类：** 建造建筑物（如电厂、兵营）或训练单位（如步兵、坦克）。指令可指定建筑/单位类型，一次只能建造或生产一个。在收到指令后，如果资源充足且未超出建造队列限制，游戏将开始建造，并在完成后通知（通过状态查询可得知）。
  - **单位控制类：** 移动和攻击命令。参赛者可以指挥己方单位移动到指定坐标或区域（引擎会自动进行路径规划），也可以命令单位对某目标发动攻击。攻击目标可以是已知的敌方单位ID或坐标（对区域进行攻击）。例如"所有坦克攻击坐标(x,y)处的敌军"。如果未指定具体单位，则默认为选中全部作战单位执行该命令。
  - **编队和选择：** 为简化操作，我们提供自动编组功能。指令可以按照单位类型或自定义编号选择一组单位。比如可以将首次生产的5个坦克设为编队1，之后通过"选中编队1"来指挥这5辆坦克一起行动。如果未显式选择，某些操作（如移动/攻击）可默认作用于全部可用战斗单位。
  - **状态查询类：** 外部指令可以请求游戏返回某些状态信息，以供决策。可查询的信息包括：当前资源值（如金钱/矿石）、已建成建筑列表、当前存活的己方单位及数量（按类型）、已发现的敌方单位/建筑概况等。查询结果将通过接口以 JSON 响应形式提供，参赛者的AI模块可将其转述给 LLM 用于后续决策或直接反馈给用户。例如用户问"我们还有多少坦克？"，AI副官可以查询后回答。
- **指令执行反馈：** 每当游戏执行完一条指令，接口会返回一个简要的结果或确认。例如建造指令会返回建造是否成功开始；移动指令在单位开始移动时确认已执行；攻击指令可能返回目标是否在射程等。如果指令格式有误或当前无法执行（例如建造条件不满足），接口会返回错误信息，以便AI模块调整策略。**注意：** 游戏操作具有 **实时性**，多数指令在执行后需要一定时间完成，例如建筑需要建造时间，单位移动也非瞬时。参赛者应考虑这种延迟，在需要时通过持续查询更新状态或由AI自主规划等待时间。
- **内部AI逻辑调整：** 玩家一方由外部AI完全控制，因此游戏内禁用了玩家阵营的自动AI逻辑（即玩家单位不会自行行动，必须依赖指令）。敌方则采用内置的标准AI脚本（经过适当简化以配合比赛节奏）。这样确保比赛中 **胜负取决于参赛者AI副官的决策质量**。内置AI会遵循常规战术进攻玩家基地，以测试参赛者AI的防御和指挥能力。
- **其他功能：** 我们保留了游戏的 **暂停** 和 **调整速度** 功能，以便调试。参赛者在测试时可暂定游戏、单步执行指令，或放慢游戏速度观察AI行为。但在最终演示和评审时，应以正常速度运行以模拟真实对战环境。此外，游戏日志记录了所有通过接口接收到的指令和执行结果，参赛者可以通过日志来调试行为或在出错时排查问题。日志不作为比赛评分依据，但可用于技术复现和分析。

简而言之，修改版 OpenRA 赋予参赛者 **类似"上帝视角"** 的控制接口：可以像游戏引擎的指挥官一样下达各种指令而无需鼠标键盘操作。它抽象出了RTS游戏中的关键动作，以易于LLM处理的格式开放给参赛者。参赛者不需要修改游戏源码，只需利用这些接口设计上层AI逻辑，让AI副官读懂人类语言并 **正确调用游戏动作**。这种架构既降低了开发难度，又确保每支参赛队伍在相同规则下进行比拼。

## 示例接口与指令格式说明

为帮助参赛者理解如何与修改版 OpenRA 交互，下面提供 **接口使用示例** 和 **指令格式说明**。接口基于 JSON 格式通信，所有指令均由一个动作（action）字段和若干参数构成。

假设游戏接口服务运行在本地`localhost:5000`端口，接受 HTTP POST 请求，URL 为`/command`。每次请求发送一条指令，服务将返回执行结果。以下是常见操作的指令示例：

- **建造建筑示例：** 建设一座电厂（Power Plant）。
   请求(JSON)：`{"action": "build", "target": "PowerPlant"}`
   含义：在基地附近建造一座电厂。如果资源足够且当前没有其他电厂在建，则开始建造。
   响应(JSON)：`{"status": "OK", "message": "Building PowerPlant started"} `
   （注：建筑物位置通常默认建在基地周边空地，如需指定位置，可附加坐标参数，如`"position": [x,y]`）
- **训练单位示例：** 生产五名士兵。
   请求：`{"action": "train", "unit": "Infantry", "quantity": 5}`
   含义：在已建成的兵营中训练5个步兵单位。若兵营尚未建造或资源不足，返回错误。
   响应：`{"status": "OK", "message": "Training 5 Infantry in progress"} `
   （游戏会将训练队列排入兵营，一段时间后步兵陆续出现）
- **移动单位示例：** 将所有坦克移动到指定坐标。
   请求：`{"action": "move", "target": "position", "position": [120, 80], "group": "all_tanks"}`
   含义：将当前己方所有坦克单位移动到地图坐标(120,80)处集合。如果之前定义过编队，也可用`"group": <id>`指定特定编队的单位。
   响应：`{"status": "OK", "message": "Moving 3 tanks to (120,80)"} `
   （假定当前有3辆坦克存活，则会移动这3辆坦克）
- **攻击指令示例：** 命令编队1攻击敌方目标。
   请求：`{"action": "attack", "target": "enemy", "enemy_id": 42, "group": 1}`
   含义：指挥先前保存为编队1的单位（例如5辆坦克）集火攻击ID为42的敌方单位。如果提供enemy_id，游戏将在当前可见敌军中匹配该ID并攻击；如省略enemy_id且目标设为"enemy"，则默认攻击最近的敌对单位或向敌方基地进军。
   响应：`{"status": "OK", "message": "Group 1 attacking enemy 42"} `
   （如果敌军ID无效或当前无可见敌军，则返回`{"status":"error","message":"enemy not found"}`）
- **查询状态示例：** 询问当前资源和军队情况。
   请求：`{"action": "query", "subject": "status"}`
   含义：获取我方当前的重要状态。
   响应：`{"status": "OK", "resources": 5000, "units": {"Infantry": 10, "Tank": 3}, "structures": ["Barracks","WarFactory","PowerPlant"]}`
   这个响应表示当前我方资源为5000（资金），拥有步兵10人、坦克3辆，已建成建筑有兵营、战车工厂和电厂各一。参赛者可以将这些数据转述给用户或交由 LLM 处理决策。
- **混合指令序列示例：** 用户可能一句话包含多个动作，例如"立即造三个矿场然后派所有坦克进攻敌人基地"。AI 副官需要拆分为两步：先建造矿场，再发起攻击。这可以通过连续发送两条指令实现：
  1. `{"action":"build", "target":"OreRefinery"}` (执行三次)
  2. `{"action":"attack", "target":"enemy_base", "group":"all_tanks"}` (`enemy_base`为一种特殊标识，游戏将选取敌方基地建筑或主基地坐标作为目标)
      执行结果分别会返回建造开始确认和攻击命令确认。参赛者需设计机制确保指令按序执行，例如等待前三个建筑指令完成或资源足够，再发送攻击命令。这可以通过轮询查询建筑完成状态来实现，或通过 LLM 输出计划时即考虑先后顺序。

**接口细节：**
 所有请求和响应均为JSON格式，字符编码UTF-8。对于HTTP接口，请求使用POST方法，`Content-Type: application/json`。也支持通过 WebSocket 长连接发送指令，在这种模式下响应也通过消息推送返回。每条指令处理时间通常在几十毫秒内（不含实际游戏完成动作的时间），接口会立即返回接受确认或错误。对于需要较长时间的任务（如建造、训练），参赛者应通过查询指令监控完成进度。

**错误处理：** 如果JSON格式不正确或缺少必要字段，接口返回`{"status":"error","message":"invalid command"}`。如果指令合法但当前状态无法执行（如重复建造已存在的建筑），`message`中会解释原因。参赛者的AI模块应对这些错误有所处理，例如当建造失败时可以尝试调整策略或通知用户。

**语音输入示例：** 若实现语音控制，流程通常是：语音转文字（ASR）得到指令文本 -> LLM将自然语言翻译为上述JSON指令 -> 通过接口执行 -> 游戏动作发生 -> AI副官通过TTS语音播报结果给用户（可选）。例如用户说："基地周围部署5个炮台"，系统将识别为文本"基地周围部署5个炮台"，LLM解析输出多条`build`指令（5个防御炮台），依次发送游戏执行。由于语音识别和合成不是比赛要求重点，此处不详述接口；参赛者可使用科大讯飞、微软 Azure 等语音服务，将语音转换为文本指令，再按前述流程处理。

以上示例展示了接口的基本用法。**参赛者在实现时，可以自行扩展更高层的指令含义**，例如将"防守基地"这样的抽象命令映射为一系列具体操作（造防御塔、派兵巡逻等）。关键是确保 AI 副官模块 **最终发出的底层指令** 符合上述格式，以便游戏正确执行。

## 推荐开发框架与 API 格式说明

为顺利开发 AI 副官模块，我们建议参赛者考虑使用成熟的框架和工具，并遵循清晰的 API 设计原则。以下是一些推荐：

- **编程语言与框架：** 建议使用 **Python** 语言来实现指令生成调度模块，因为 Python 拥有丰富的机器学习和HTTP通信库，可以方便地调用 LLM 接口和发送游戏指令。推荐使用的库和框架包括：
  - **HTTP 请求库：** 如 `requests` 或 `httpx`，用于向游戏引擎的指令接口发送 HTTP 请求，以及接收返回结果。如果使用 WebSocket，可考虑`websockets`或`websocket-client`库。
  - **LLM 接口库：** 如果使用 OpenAI API，可直接使用官方库 `openai` 调用 GPT 模型；如使用其他云端模型，使用其提供的SDK。如采用本地运行模型（DeepSeek 等），可使用 `requests` 调用其 REST 接口，或通过其 Python SDK（如果有）加载模型。本地部署的 DeepSeek 模型服务通常会提供 OpenAI兼容接口或OpenAPI文档，可参考配置说明。
  - **Prompt 架构框架：** 可选用 **LangChain** 等对话式AI编排框架。LangChain 支持将自定义"工具"（tools）集成到 LLM 推理中，能够帮助将游戏动作接口封装成工具函数，让 LLM 学会调用。例如，可为"建造建筑"定义一个工具函数，LangChain 负责根据 LLM 输出决定何时调用此函数，从而执行游戏指令。这种方式利用了 LLM 的函数调用能力，使指令格式和调用变得简洁可靠。此外，也可考虑 OpenAI 最新的 **Function Calling** 特性，通过定义函数签名（如`build(structure: str) -> None`）让模型直接输出结构化的函数调用，再映射为游戏指令。
  - **多线程/异步框架：** 在需要同时监听游戏返回和发送新指令时，可使用 Python 的 `asyncio` 或多线程库。比如开启一个线程持续获取游戏状态（查询指令轮询），另一个线程接受用户新指令并调用LLM生成动作，确保指挥调度流畅。
  - **语音处理库（可选）：** 如果实现语音输入/输出，可使用开源的 Whisper 模型进行语音转文本，或调用科大讯飞、Azure Speech API 等服务。语音合成可采用 pyttsx3 等本地库或腾讯云TTS等。请注意控制音频处理的延迟，以免指令执行滞后。
- **API 格式与约定：** 参赛者应设计清晰的模块接口，方便与游戏引擎和 LLM 交互。典型的模块划分如下：
  - **指令解析模块：** 负责将 **用户的自然语言** 转化为 **游戏指令序列**。可以通过Prompt设计，引导 LLM 输出规范的 JSON 指令格式。例如在 Prompt 中告知模型可用的指令动作和格式，然后让模型根据输入输出 JSON。这部分可以封装为函数，如 `parse_command(user_input: str) -> List[dict]`，输出一系列游戏指令。利用 LLM 时，需做好特殊情况处理，比如用户指令不明确时模型可能需要询问澄清（可选实现）。
  - **指令调度模块：** 负责 **执行和管理指令序列**。接收解析模块产生的指令列表后，依次向游戏接口发送，并根据游戏返回和当前战局状况决定是否调整顺序或插入新的指令。例如，如果用户下达"全面进攻"后又说"撤退"，调度模块应支持中止尚未完成的攻击命令（可能通过发送停止指令实现）。调度模块也可以实现一些 **策略逻辑**，比如在缺乏资源时等待资源累积再建造，或连续建造需要排队等。
  - **状态更新模块：** 定期（如每隔1秒）从游戏获取关键信息，更新内存中战场状态。可以维护一个GameState对象，属性包括当前资源值、我方单位和建筑数量、敌方已发现单位等。这些信息可作为额外上下文提供给 LLM，使其决策更合理（例如如果发现敌军坦克很多，LLM 可以选择建造反坦克炮塔）。
  - **交互接口模块：** 与用户的交互部分。如文本界面可以简单打印与读取命令；图形界面可选用简易GUI或网页界面展示游戏画面和对话框；语音接口则处理麦克风输入和扬声器输出。该模块主要负责把用户指令交给解析模块、将AI副官的文字回复或语音反馈给用户，以提升可用性。
- **工具与测试：** 推荐参赛者充分利用现有测试框架和调试工具。例如编写 **单元测试** 验证指令解析模块对各种输入的正确处理；使用游戏的 **回放功能**（如果有）或者录制大量指令脚本进行压力测试。主办方提供的示例里包含了一些典型指令序列，参赛者可在此基础上扩展测试用例。调试LLM输出时，可暂时使用 **小模型** 或离线模型快速迭代Prompt，待格式稳定后再切换高性能模型，以降低开发成本。
****
总的来说，参赛者的系统应遵循 **模块解耦、接口清晰、稳健容错** 的原则。尤其在LLM输出不稳定的情况下，加入必要的验证（如检查JSON格式合法性，未知指令词过滤）和fallback机制（如模型多次出错时给用户提示或者切换规则策略）。主办方提供的框架和建议并非强制要求，参赛者可以根据自身擅长选择合适的实现方式，但需保证最终作品能够顺利对接游戏，引导 LLM 完成游戏控制任务。

## 评分机制与评委机制设计

为客观、公平地评估参赛作品，本次比赛制定了 **多维度的评分机制**，由专业评委组根据明确的标准对每个作品进行打分。具体评分维度、权重和评审流程如下：

**1. 评分维度与权重：**

- **技术实现 (40%)：** 作品功能完备性和技术难度。评委将考察 AI 副官是否能够正确解析各种自然语言指令，并通过接口有效控制游戏达成相应目的。例如，能否处理连续复杂命令，是否支持语音输入，是否具备一定自主性（如紧急情况自动应对）。同时看重系统架构合理性、代码质量和稳定性。包含模块设计的清晰程度、错误处理健壮性、对实时性的支持等。
- **AI 智能与表现 (30%)：** AI 副官的智能水平和在游戏中的实际效果。评委会观看演示视频并在必要时亲自运行作品，评价 AI 副官对自然语言指令的理解是否准确，执行策略是否高效合理。例如用户下达模糊指令时，AI是否能正确领会意图并拆解为具体行动；面对敌方进攻时，是否能迅速作出防御反应。还包括 AI 副官与用户交互的友好度（如是否会用语言反馈执行情况、提醒资源不足等）。这一维度反映作品在 **模拟人类副官** 方面的逼真程度和实用价值。
- **创新创意 (15%)：** 方案的创新性和独特亮点。评委将关注参赛者是否在基本要求之外有所拓展和创意贡献。例如使用了新颖的策略规划方法、结合了多模态交互（如图像识别战场态势）、优化了LLM Prompt使指令解析更高效，或者在用户体验上做了特别设计（如UI界面、角色化的副官对话风格）。创新可以是技术上的，也可以是呈现形式上的。具有突出亮点的作品将在此维度获得高分。
- **演示与文档 (10%)：** 提交材料的完整度和质量。包括演示视频的清晰度、演示流程设计以及解说是否到位；项目文档（README/说明书）的详细程度和可读性；代码注释和组织是否方便评审理解。好的演示应条理清楚地展示功能，文档应能让人快速了解如何运行及实现原理。这个维度鼓励参赛者注重 **成果的呈现**，方便评委和公众交流学习。
- **合作与规范 (5%)：** 遵守比赛规则及团队协作情况。作品如严格按照接口规范实现、没有违规使用外部数据或作弊，将获得满分。若团队合作完成，评委可能会参考团队在开发过程中的分工协作情况（可以在文档中注明每个成员贡献）。同时，凡及时在社区分享经验、提出建设性反馈帮助改进比赛的团队，可获得评委认可。此项也包含对 **原创性** 的要求，如发现抄袭或高度同，则该项记零且可能被取消资格。

**2. 评委组结构：** 评委组将由 **5名专家** 组成，覆盖不同领域以确保评价的全面性：

- 2 名资深游戏开发工程师（熟悉 OpenRA 或 RTS 游戏AI开发），关注技术实现细节和游戏效果。
- 1 名自然语言处理/人工智能专家（来自高校或研究机构），侧重评估 LLM 应用的合理性和智能水平。
- 1 名人机交互专家，关注用户体验、交互创新方面。
- 1 名主办方代表（赛事负责人或赞助企业技术主管），综合考量作品完成度与规则符合度。

评委将在赛前共同研讨评分标准，确保理解一致，并签署保密协议避免利益冲突。每个评委将独立审阅所有作品材料，给出各维度评分。

**3. 打分流程：**

- **初步筛选：** 由主办方技术团队先行验证每份提交是否符合基本要求，能否运行。如有明显不符合规定者（例如缺少关键组成、无法运行），将记录问题并通知评委。必要时这类作品可被淘汰或扣分。
- **个别评审：** 每位评委对照评分维度，观看参赛视频、阅读文档和代码片段，并在自己的测试环境中运行作品（主办方将提供统一的测试电脑配置和环境，以尽量公平）。评委针对每个作品在每个维度打分（0-10或0-100分制，内部换算成权重分）。评委也会撰写简短评语，指出优点和不足。
- **集体评议：** 所有作品评分完成后，评委组召开线上会议，汇总每个作品的得分情况。对于评分差异较大的作品，评委将讨论并交换意见，必要时可调整个别维度分数（例如某评委遗漏了视频中一些表现，可在讨论后修正）。确保最终每件作品都有一致的综合评分。
- **排名与获奖确定：** 按照综合得分由高到低排序，确定名次。原则上分数最高者为一等奖，第二高为二等奖，第三高为三等奖。但如出现 **总分非常接近** 或 **维度分布有特殊情况**，评委可酌情讨论，例如并列情况或特别奖项可能性。如果出现并列，同奖项数量可适当增加，或通过加试（例如要求补充展示AI对另一组隐藏指令的反应）来决出高下。
- **结果审核：** 主办方对评选结果进行最终审核，确保符合预算和奖项设置，无违规争议。然后准备对外公布材料，包括获奖者名单、作品亮点点评等。

评分过程中，评委将秉持公平、公正、透明的原则。不同行业背景的评委评分有差异属于正常，将通过集体评议平衡。参赛者提交的所有材料仅用于本次评审，评委不得挪作他用。如参赛者对评审结果有疑问，可在结果公布后一周内书面提出，主办方将组织复核并给予答复。

## 奖项设置

本次比赛设立丰厚奖项以激励优秀作品，奖品总价值约人民币 **6万元**。奖项及奖励如下：

- **一等奖（1名）：** 高性能笔记本电脑 *"5090"* 型号一台。
   *奖品描述：* 搭载 NVIDIA GeForce RTX 5090 GPU 的旗舰级电脑，顶尖配置保证AI开发和游戏运行流畅。 (预计价值 ~¥25,000)
- **二等奖（1名）：** Apple MacBook Pro 笔记本电脑（一台，M4 Pro 芯片款）。
   *奖品描述：* Apple 最新款 MacBook Pro (假设配备M4 Pro芯片)，拥有出色的AI开发环境和强劲算力，用于机器学习训练亦游刃有余。 (预计价值 ~¥18,000)
- **三等奖（1名）：** Apple iPad Pro 平板电脑（一台，最新款）。
   *奖品描述：* 配备高刷新率屏幕和强大芯片的 iPad Pro，可用于展示作品、移动开发或作为辅助控制终端。 (预计价值 ~¥8,000)
- **优胜奖（若干）：** 对未获前三但表现优异的团队颁发 **优胜证书** 及奖品纪念。可能包括价值¥1000左右的科技礼品（如智能音箱、机械键盘等，以实际赞助为准）。数量根据参赛作品整体质量确定。

每位获奖者（团队）将同时获得由主办方颁发的 **获奖证书** 和奖杯，以兹鼓励。在官网和宣传渠道公布获奖名单及作品简介。

*注：奖品图片仅供参考，以实物为准。若因特殊原因相应型号库存不足，主办方有权用同等价值的近似奖品替代。奖品所涉个人所得税由主办方依法代扣代缴。团队参赛获奖的，由团队自行协商奖品归属。*

此外，所有成功提交作品的参赛者都将获得 **电子参赛证明**（可用于简历等），以感谢参与。表现突出者还可能有机会获得主办单位提供的 **实习机会或合作邀请**（视赞助企业政策而定）。

## 开源与展示计划

比赛结束后，主办方将推进 **开源和成果展示** 计划，扩大赛事影响力，促进技术共享：

- **比赛环境开源：** 我们修改的 OpenRA 引擎及配套 MCP 接口模块将以 GPL 开源协议发布在 GitHub 仓库。这样不仅参赛者可以继续使用完善自己的项目，其他对游戏AI感兴趣的开发者也能利用该引擎进行研究和创新。我们会在仓库附上完善的文档和示例，降低上手难度。**发布时间：** 预计在比赛结果公布后一周内公开代码。
- **优秀作品开源展示：** 鼓励获得一二三等奖的团队将他们的 AI 副官源码开源。经团队同意后，主办方将在官方仓库下创建 **Winners** 专区，收录获奖作品的代码链接、技术说明和演示视频。对于愿意公开代码的队伍，我们也会提供协助如代码仓库托管、License选择等。即使未公开完整源码，我们也希望能分享部分核心思路或模型Prompt，供社区学习讨论。*（尊重参赛者意愿，非强制要求开源，但我们会强调开源带来的影响力提升。）*
- **演示视频平台：** 主办方将搭建比赛成果展示页面，在其上播放所有获奖作品的演示视频，并配以文字介绍。同时，将精选若干有代表性的参赛作品视频在B站、YouTube等平台发布推广。通过媒体宣传，把"LLM 自然语言操控红警"的创意呈现给更广泛的观众，激发公众对游戏AI的兴趣。我们也可能联系业内知名技术自媒体对比赛结果进行专题报道。
- **线下展示活动（拟）：** 如果条件允许，主办方将在赛后适时举办一场线下沙龙或研讨会，邀请获奖团队分享经验，进行现场演示互动，并讨论该领域的未来发展。现场将开放体验：观众可实际用语音/文字指挥游戏，看AI副官执行，以直观感受技术成果。这将有助于促成 **产学研** 更深入的交流合作。
- **后续开发支持：** 主办方计划在比赛基础上持续改进该开源项目，例如加入更多游戏支持（扩展到《星际争霸》等）、提升MCP工具生态等。我们将保持与参赛者社区的联系，定期更新项目进展。对特别优秀的创意，有可能寻求产业孵化或进一步研究的机会。

在公开过程中，我们将充分署名参赛者的成果，保护其知识产权和贡献荣誉。如有团队不愿公开其代码或细节，我们也会尊重其选择，仅发布其演示效果。整个开源与展示计划旨在 **弘扬开源精神**、**扩大技术影响**，让比赛成果真正服务于更多开发者和AI爱好者，推动"AI 副官"概念的进一步研究与实现。