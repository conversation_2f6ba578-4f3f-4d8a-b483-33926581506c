<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenRA Copilot Server</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            width: 100%;
            max-width: none;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            box-sizing: border-box;
        }
        .video-container {
            text-align: center;
            margin: 20px 0;
            overflow: visible;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 200px;
        }
        video {
            border: 1px solid #ccc;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
            margin: 20px 0;
        }
        .control-group {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .game-status {
            margin-left: 10px;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .game-status.running {
            background-color: #d4edda;
            color: #155724;
        }
        .game-status.stopped {
            background-color: #f8d7da;
            color: #721c24;
        }
        .command-group {
            flex-direction: column;
            align-items: flex-start !important;
            width: 100%;
        }
        .command-group textarea {
            width: 100%;
            min-height: 80px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            resize: vertical;
        }
        .command-group button {
            margin: 0;
        }
        .response-group {
            flex-direction: column;
            align-items: flex-start !important;
            width: 100%;
        }
        .response-group textarea {
            width: 100%;
            min-height: 120px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            resize: vertical;
            background-color: #f8f9fa;
        }
        .response-group button {
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>OpenRA Copilot Server</h1>
        <p>这是一个 WebRTC 游戏转发系统，用于 OpenRA Copilot 功能。</p>
        
        <!-- 游戏控制区域 -->
        <div class="controls">
            <div class="control-group">
                <label>游戏控制:</label>
                <button id="startGameButton">启动游戏</button>
                <button id="stopGameButton" disabled>停止游戏</button>
                <span id="gameStatus" class="game-status">游戏未运行</span>
            </div>
            
            <div class="control-group">
                <label>存档选择:</label>
                <select id="loadSaveSelect">
                    <option value="01" selected>01</option>
                    <option value="02">02</option>
                    <option value="03">03</option>
                </select>
            </div>
        </div>
        
        <!-- WebRTC控制区域 -->
        <div class="controls">
            <div class="control-group">
                <label>WebRTC连接:</label>
                <button id="startButton" disabled>开始连接</button>
                <button id="stopButton" disabled>停止连接</button>
            </div>
            
            <div class="control-group">
                <label>捕获方式:</label>
                <button id="prevCapture">上一个</button>
                <select id="captureList"></select>
                <button id="nextCapture">下一个</button>
            </div>
            
            <div class="control-group">
                <label>分辨率:</label>
                <button id="prevResolution">上一个</button>
                <select id="resolutionList"></select>
                <button id="nextResolution">下一个</button>
            </div>
        </div>
        
        <!-- 命令发送区域 -->
        <div class="controls">
            <div class="control-group command-group">
                <label>游戏命令:</label>
                <textarea id="commandInput" placeholder='输入JSON格式的命令，例如: {"action": "move", "unit": "tank"} 或 {"command": "move", "params": {"unit": "tank"}}'></textarea>
                <button id="sendCommandButton" disabled>发送命令</button>
            </div>
        </div>
        
        <!-- 服务器响应区域 -->
        <div class="controls">
            <div class="control-group response-group">
                <label>服务器响应:</label>
                <textarea id="responseOutput" readonly placeholder="服务器响应将显示在这里..."></textarea>
                <button id="clearResponseButton">清空响应</button>
            </div>
        </div>
        
        <div id="status" class="status"></div>
        <div id="info" class="info" style="display: none;"></div>
        
        <div class="video-container">
            <video id="video" autoplay playsinline></video>
        </div>
    </div>

    <script>
        let pc;
        let localStream;
        let currentMode = 'camera';
        let currentResolution = { width: 1280, height: 720 };

        const startButton = document.getElementById('startButton');
        const stopButton = document.getElementById('stopButton');
        const video = document.getElementById('video');
        const statusDiv = document.getElementById('status');
        const infoDiv = document.getElementById('info');
        const prevCapture = document.getElementById('prevCapture');
        const nextCapture = document.getElementById('nextCapture');
        const captureList = document.getElementById('captureList');
        const prevResolution = document.getElementById('prevResolution');
        const nextResolution = document.getElementById('nextResolution');
        const resolutionList = document.getElementById('resolutionList');
        
        // 游戏控制元素
        const startGameButton = document.getElementById('startGameButton');
        const stopGameButton = document.getElementById('stopGameButton');
        const gameStatus = document.getElementById('gameStatus');
        const loadSaveSelect = document.getElementById('loadSaveSelect');
        const commandInput = document.getElementById('commandInput');
        const sendCommandButton = document.getElementById('sendCommandButton');
        const responseOutput = document.getElementById('responseOutput');
        const clearResponseButton = document.getElementById('clearResponseButton');

        function updateStatus(message, isError = false) {
            statusDiv.textContent = message;
            statusDiv.className = `status ${isError ? 'error' : 'success'}`;
        }

        function updateInfo(message) {
            infoDiv.textContent = message;
            infoDiv.style.display = 'block';
        }

        function appendResponse(message) {
            const timestamp = new Date().toLocaleTimeString();
            const formattedMessage = `[${timestamp}] ${message}\n`;
            responseOutput.value += formattedMessage;
            responseOutput.scrollTop = responseOutput.scrollHeight;
        }

        // 游戏状态管理
        let gameRunning = false;
        let statusCheckInterval = null;

        function updateGameStatus(running) {
            gameRunning = running;
            if (running) {
                gameStatus.textContent = '游戏运行中';
                gameStatus.className = 'game-status running';
                startGameButton.disabled = true;
                stopGameButton.disabled = false;
                startButton.disabled = false;
                sendCommandButton.disabled = false;
            } else {
                gameStatus.textContent = '游戏未运行';
                gameStatus.className = 'game-status stopped';
                startGameButton.disabled = false;
                stopGameButton.disabled = true;
                startButton.disabled = true;
                sendCommandButton.disabled = true;
                
                // 如果游戏停止，也停止WebRTC连接
                if (pc && pc.connectionState !== 'closed') {
                    stopConnection();
                }
            }
        }

        async function checkGameStatus() {
            try {
                const response = await fetch('/game_status');
                const data = await response.json();
                const wasRunning = gameRunning;
                updateGameStatus(data.running);
                
                // 如果状态发生变化，在响应框中显示
                if (wasRunning !== data.running) {
                    appendResponse(`游戏状态变化: ${data.running ? '运行中' : '已停止'}`);
                }
            } catch (error) {
                console.error('检查游戏状态失败:', error);
            }
        }

        function startStatusCheck() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }
            statusCheckInterval = setInterval(checkGameStatus, 2000); // 每2秒检查一次
        }

        function stopStatusCheck() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
                statusCheckInterval = null;
            }
        }

        function updateVideoSize(width, height) {
            // 以窗口宽度为准，但不超过视频原始尺寸
            const screenWidth = window.innerWidth;
            const aspectRatio = width / height;
            
            // 计算适合窗口宽度的尺寸
            let newWidth = screenWidth - 100; // 留出边距
            let newHeight = newWidth / aspectRatio;
            
            // 确保不超过视频原始尺寸
            if (newWidth > width) {
                newWidth = width;
                newHeight = height;
            }
            
            // 设置视频尺寸
            video.style.width = `${newWidth}px`;
            video.style.height = `${newHeight}px`;
            
            // 动态调整容器尺寸以适应大分辨率
            const container = document.querySelector('.container');
            if (newWidth > 1200) {
                container.style.maxWidth = `${newWidth + 100}px`;
            } else {
                container.style.maxWidth = 'none';
            }
            
            updateInfo(`视频尺寸: ${width}x${height} (显示: ${Math.round(newWidth)}x${Math.round(newHeight)})`);
        }

        // 刷新捕获方法列表
        async function refreshCaptureList() {
            try {
                const response = await fetch('/capture_methods');
                const data = await response.json();
                captureList.innerHTML = '';
                data.methods.forEach(method => {
                    const option = document.createElement('option');
                    option.value = method.index;
                    option.textContent = method.name;
                    if (method.index === data.current_index) {
                        option.selected = true;
                    }
                    captureList.appendChild(option);
                });
            } catch (error) {
                console.error('获取捕获方法列表失败:', error);
            }
        }

        // 刷新分辨率列表
        async function refreshResolutionList() {
            try {
                const response = await fetch('/resolutions');
                const data = await response.json();
                resolutionList.innerHTML = '';
                data.resolutions.forEach(resolution => {
                    const option = document.createElement('option');
                    option.value = resolution.index;
                    option.textContent = `${resolution.name} (${resolution.width}x${resolution.height})`;
                    if (resolution.index === data.current_index) {
                        option.selected = true;
                        currentResolution = { width: resolution.width, height: resolution.height };
                    }
                    resolutionList.appendChild(option);
                });
            } catch (error) {
                console.error('获取分辨率列表失败:', error);
            }
        }

        // 自动重新连接函数
        async function autoReconnect() {
            if (pc && pc.connectionState !== 'closed') {
                updateStatus('正在自动重新连接...');
                await stopConnection();
                await startConnection();
            }
        }

        // 切换到上一个捕获方法
        prevCapture.addEventListener('click', async () => {
            try {
                const response = await fetch('/capture_methods');
                const data = await response.json();
                const currentIndex = data.current_index;
                const newIndex = (currentIndex - 1 + data.methods.length) % data.methods.length;
                
                await fetch('/set_capture_mode', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ index: newIndex })
                });
                
                await refreshCaptureList();
                updateStatus(`已切换到上一个捕获方式: ${data.methods[newIndex].name}`);
                await autoReconnect();
            } catch (error) {
                updateStatus(`切换失败: ${error.message}`, true);
            }
        });

        // 切换到下一个捕获方法
        nextCapture.addEventListener('click', async () => {
            try {
                const response = await fetch('/capture_methods');
                const data = await response.json();
                const currentIndex = data.current_index;
                const newIndex = (currentIndex + 1) % data.methods.length;
                
                await fetch('/set_capture_mode', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ index: newIndex })
                });
                
                await refreshCaptureList();
                updateStatus(`已切换到下一个捕获方式: ${data.methods[newIndex].name}`);
                await autoReconnect();
            } catch (error) {
                updateStatus(`切换失败: ${error.message}`, true);
            }
        });

        // 下拉列表选择切换捕获方法
        captureList.addEventListener('change', async () => {
            try {
                const selectedIndex = parseInt(captureList.value);
                await fetch('/set_capture_mode', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ index: selectedIndex })
                });
                
                const response = await fetch('/capture_methods');
                const data = await response.json();
                updateStatus(`已切换捕获方式: ${data.methods[selectedIndex].name}`);
                await autoReconnect();
            } catch (error) {
                updateStatus(`切换失败: ${error.message}`, true);
            }
        });

        // 切换到上一个分辨率
        prevResolution.addEventListener('click', async () => {
            try {
                const response = await fetch('/resolutions');
                const data = await response.json();
                const currentIndex = data.current_index;
                const newIndex = (currentIndex - 1 + data.resolutions.length) % data.resolutions.length;
                
                await fetch('/set_resolution', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ index: newIndex })
                });
                
                await refreshResolutionList();
                updateStatus(`已切换到上一个分辨率: ${data.resolutions[newIndex].name}`);
                updateVideoSize(currentResolution.width, currentResolution.height);
                await autoReconnect();
            } catch (error) {
                updateStatus(`切换失败: ${error.message}`, true);
            }
        });

        // 切换到下一个分辨率
        nextResolution.addEventListener('click', async () => {
            try {
                const response = await fetch('/resolutions');
                const data = await response.json();
                const currentIndex = data.current_index;
                const newIndex = (currentIndex + 1) % data.resolutions.length;
                
                await fetch('/set_resolution', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ index: newIndex })
                });
                
                await refreshResolutionList();
                updateStatus(`已切换到下一个分辨率: ${data.resolutions[newIndex].name}`);
                updateVideoSize(currentResolution.width, currentResolution.height);
                await autoReconnect();
            } catch (error) {
                updateStatus(`切换失败: ${error.message}`, true);
            }
        });

        // 下拉列表选择切换分辨率
        resolutionList.addEventListener('change', async () => {
            try {
                const selectedIndex = parseInt(resolutionList.value);
                await fetch('/set_resolution', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ index: selectedIndex })
                });
                
                const response = await fetch('/resolutions');
                const data = await response.json();
                updateStatus(`已切换分辨率: ${data.resolutions[selectedIndex].name}`);
                updateVideoSize(currentResolution.width, currentResolution.height);
                await autoReconnect();
            } catch (error) {
                updateStatus(`切换失败: ${error.message}`, true);
            }
        });

        // 页面加载时初始化
        window.addEventListener('load', async () => {
            await refreshCaptureList();
            await refreshResolutionList();
            updateVideoSize(currentResolution.width, currentResolution.height);
            
            // 检查游戏状态并开始定期检查
            await checkGameStatus();
            startStatusCheck();
        });
        
        // 监听窗口大小变化，自动调整视频尺寸
        window.addEventListener('resize', () => {
            if (currentResolution) {
                updateVideoSize(currentResolution.width, currentResolution.height);
            }
        });

        // 停止连接的函数
        async function stopConnection() {
            if (pc) {
                pc.close();
                pc = null;
            }
            if (video.srcObject) {
                video.srcObject.getTracks().forEach(track => track.stop());
                video.srcObject = null;
            }
            startButton.disabled = false;
            stopButton.disabled = true;
        }

        // 开始连接的函数
        async function startConnection() {
            try {
                // 检查游戏是否运行
                if (!gameRunning) {
                    updateStatus('请先启动游戏', true);
                    return;
                }
                
                updateStatus('正在建立连接...');
                startButton.disabled = true;

                // 创建 RTCPeerConnection，添加更多 STUN 服务器
                pc = new RTCPeerConnection({
                    iceServers: [
                        { urls: 'stun:stun.l.google.com:19302' },
                        { urls: 'stun:stun1.l.google.com:19302' },
                        { urls: 'stun:stun2.l.google.com:19302' }
                    ],
                    iceCandidatePoolSize: 10
                });

                // 处理远程流
                pc.ontrack = (event) => {
                    if (event.streams && event.streams[0]) {
                        video.srcObject = event.streams[0];
                        updateStatus('连接成功！正在接收视频流...');
                    }
                };

                // 处理连接状态变化
                pc.onconnectionstatechange = () => {
                    console.log('Connection state:', pc.connectionState);
                    if (pc.connectionState === 'failed') {
                        updateStatus('连接失败，请重试', true);
                        startButton.disabled = false;
                    }
                };

                // 处理 ICE 连接状态
                pc.oniceconnectionstatechange = () => {
                    console.log('ICE connection state:', pc.iceConnectionState);
                };

                // 创建 offer，明确指定接收视频
                const offer = await pc.createOffer({
                    offerToReceiveVideo: true,
                    offerToReceiveAudio: false
                });
                await pc.setLocalDescription(offer);

                // 发送 offer 到服务器
                const response = await fetch('/offer', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sdp: pc.localDescription.sdp,
                        type: pc.localDescription.type
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
                }

                const answer = await response.json();
                
                if (answer.error) {
                    throw new Error(answer.error);
                }
                
                await pc.setRemoteDescription(new RTCSessionDescription(answer));

                stopButton.disabled = false;
                updateStatus('WebRTC 连接已建立');

            } catch (error) {
                console.error('Error:', error);
                updateStatus(`连接失败: ${error.message}`, true);
                startButton.disabled = false;
                
                // 清理连接
                if (pc) {
                    pc.close();
                    pc = null;
                }
            }
        }

        startButton.addEventListener('click', startConnection);
        stopButton.addEventListener('click', stopConnection);

        // 游戏控制事件监听器
        startGameButton.addEventListener('click', async () => {
            try {
                const loadSave = loadSaveSelect.value;
                updateStatus('正在启动游戏...');
                
                const response = await fetch('/start_game', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ load_save: loadSave })
                });
                
                const data = await response.json();
                if (response.ok) {
                    updateStatus(data.message);
                    // 启动游戏后自动开始WebRTC连接
                    setTimeout(async () => {
                        await checkGameStatus();
                        if (gameRunning) {
                            await startConnection();
                        }
                    }, 3000); // 等待3秒让游戏完全启动
                } else {
                    updateStatus(data.error, true);
                }
            } catch (error) {
                updateStatus(`启动游戏失败: ${error.message}`, true);
            }
        });

        stopGameButton.addEventListener('click', async () => {
            try {
                updateStatus('正在停止游戏...');
                
                const response = await fetch('/stop_game', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });
                
                const data = await response.json();
                if (response.ok) {
                    updateStatus(data.message);
                } else {
                    updateStatus(data.error, true);
                }
            } catch (error) {
                updateStatus(`停止游戏失败: ${error.message}`, true);
            }
        });

        // 命令发送事件监听器
        sendCommandButton.addEventListener('click', async () => {
            try {
                const command = commandInput.value.trim();
                if (!command) {
                    updateStatus('请输入命令', true);
                    return;
                }

                // 验证JSON格式
                try {
                    JSON.parse(command);
                } catch (e) {
                    updateStatus('命令格式错误，请输入有效的JSON', true);
                    return;
                }

                appendResponse(`发送命令: ${command}`);

                const response = await fetch('/send_command', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ command: command })
                });

                const data = await response.json();
                if (response.ok) {
                    updateStatus('命令发送成功');
                    appendResponse(`服务器响应: ${JSON.stringify(data, null, 2)}`);
                    commandInput.value = ''; // 清空输入框
                } else {
                    updateStatus(data.error, true);
                    appendResponse(`错误响应: ${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                updateStatus(`发送命令失败: ${error.message}`, true);
                appendResponse(`发送失败: ${error.message}`);
            }
        });

        // 清空响应按钮
        clearResponseButton.addEventListener('click', () => {
            responseOutput.value = '';
        });

        // 回车键发送命令
        commandInput.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                sendCommandButton.click();
            }
        });
    </script>
</body>
</html>